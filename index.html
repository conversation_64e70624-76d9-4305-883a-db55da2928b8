import React from 'react';
import { Link } from 'react-router-dom';

const Header = () => {
  const headerStyle = {
    backgroundColor: '#CDFF9A',
    padding: '15px 50px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'relative',
    zIndex: 1000
  };

  const logoStyle = {
    fontSize: '24px',
    fontWeight: 'bold',
    color: '#000000',
    textDecoration: 'none',
    display: 'flex',
    alignItems: 'center'
  };

  const logoTextStyle = {
    display: 'flex',
    flexDirection: 'column',
    lineHeight: '1'
  };

  const navStyle = {
    display: 'flex',
    listStyle: 'none',
    margin: 0,
    padding: 0,
    gap: '30px'
  };

  const navLinkStyle = {
    textDecoration: 'none',
    color: '#000000',
    fontSize: '14px',
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: '1px',
    transition: 'color 0.3s ease'
  };

  const navLinkHoverStyle = {
    color: '#ffffff'
  };

  const iconsStyle = {
    display: 'flex',
    gap: '15px',
    alignItems: 'center'
  };

  const iconStyle = {
    width: '20px',
    height: '20px',
    cursor: 'pointer',
    transition: 'transform 0.3s ease'
  };

  return (
    <header style={headerStyle}>
      {/* Logo */}
      <Link to="/" style={logoStyle}>
        <div style={logoTextStyle}>
          <span>HA</span>
          <span>SPORT</span>
        </div>
      </Link>

      {/* Navigation */}
      <nav>
        <ul style={navStyle}>
          <li>
            <Link 
              to="/" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              HOME
            </Link>
          </li>
          <li>
            <Link 
              to="/catalog" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              CATALOG
            </Link>
          </li>
          <li>
            <Link 
              to="/blog" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              BLOG
            </Link>
          </li>
          <li>
            <Link 
              to="/shop" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              SHOP
            </Link>
          </li>
          <li>
            <Link 
              to="/contact" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              CONTACT US
            </Link>
          </li>
        </ul>
      </nav>

      {/* Icons */}
      <div style={iconsStyle}>
        {/* Search Icon */}
        <svg style={iconStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.35-4.35"></path>
        </svg>
        
        {/* Heart Icon */}
        <svg style={iconStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
        </svg>
        
        {/* User Icon */}
        <svg style={iconStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
        
        {/* Cart Icon */}
        <svg style={iconStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="9" cy="21" r="1"></circle>
          <circle cx="20" cy="21" r="1"></circle>
          <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
        </svg>
      </div>
    </header>
  );
};

export default Header;
import React from "react";
import {
  FaFacebookF,
  FaTwitter,
  FaInstagram,
  FaLinkedinIn,
} from "react-icons/fa";
import { FaArrowLeft, FaArrowRight } from "react-icons/fa";
import img01 from "./../imgs/01.png";
import img02 from "./../imgs/Image1.png";
const HomePage = () => {
  return (
    <div
      style={{
        display: "flex",
        minHeight: "100vh",
        backgroundColor: "#07272E",
        paddingLeft: "100px",
        paddingRight: "20px",
        paddingBottom: "80px",
      }}
    >
      <div
        style={{
          width: "5%",
          height: "calc(100vh - 70px)",
          display: "flex",
          flexDirection: "column",
          alignItems: "end",
          justifyContent: "space-between",
          paddingTop: "200px",
          paddingBottom: "80px",
          borderRight: "1px solid #fff",
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "300px",
            width: "80%",
          }}
        >
          <div
            style={{
              fontSize: "18px",
              transform: "rotate(-90deg)",
              whiteSpace: "nowrap",
              color: "#CDFF9A",
            }}
          >
            2023 Dotcreativemarket
          </div>

          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: "70px",
            }}
          >
            <div
              style={{
                fontSize: "18px",
                transform: "rotate(-90deg)",
                whiteSpace: "nowrap",
                marginBottom: "20px",
                color: "#CDFF9A",
              }}
            >
              FOLLOW US
            </div>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "20px",
                alignItems: "center",
              }}
            >
              <FaLinkedinIn
                style={{
                  color: "#ffffff",
                  fontSize: "18px",
                  cursor: "pointer",
                  transition: "color 0.3s ease",
                }}
                onMouseEnter={(e) => (e.target.style.color = "#0077B5")}
                onMouseLeave={(e) => (e.target.style.color = "#ffffff")}
              />
              <FaFacebookF
                style={{
                  color: "#ffffff",
                  fontSize: "18px",
                  cursor: "pointer",
                  transition: "color 0.3s ease",
                }}
                onMouseEnter={(e) => (e.target.style.color = "#4267B2")}
                onMouseLeave={(e) => (e.target.style.color = "#ffffff")}
              />
              <FaTwitter
                style={{
                  color: "#ffffff",
                  fontSize: "18px",
                  cursor: "pointer",
                  transition: "color 0.3s ease",
                }}
                onMouseEnter={(e) => (e.target.style.color = "#1DA1F2")}
                onMouseLeave={(e) => (e.target.style.color = "#ffffff")}
              />
              <FaInstagram
                style={{
                  color: "#ffffff",
                  fontSize: "18px",
                  cursor: "pointer",
                  transition: "color 0.3s ease",
                }}
                onMouseEnter={(e) => (e.target.style.color = "#E4405F")}
                onMouseLeave={(e) => (e.target.style.color = "#ffffff")}
              />
            </div>
          </div>
        </div>
      </div>

      <div
        style={{
          width: "40%",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          paddingLeft: "60px",
          position: "relative",
        }}
      >
        <div
          style={{
            position: "absolute",
            fontSize: "400px",
            fontWeight: "bold",
            color: "rgba(109, 139, 116, 0.3)",
            top: "40%",
            left: "45%",
            transform: "translate(-50%, -50%)",
            zIndex: 1,
            lineHeight: "1",
            fontFamily: "Arial, sans-serif",
          }}
        >
          <img
            src={img01}
            alt="Logo"
            style={{ width: "900px", height: "213px" }}
          />
        </div>

        <div
          style={{
            position: "relative",
            zIndex: 2,
            top: "150px",
            left: "70px",
          }}
        >
          <div
            style={{
              fontSize: "17px",
              color: "#CDFF9A",
              marginBottom: "30px",
              letterSpacing: "2px",
              fontWeight: "400",
            }}
          >
            BEGIN BREAKING YOUR LIMIT
          </div>

          <h1
            style={{
              fontSize: "60px",
              fontWeight: "500px",
              color: "#ffffff",
              lineHeight: "1.1",
              marginBottom: "50px",
              fontFamily: "Arial, sans-serif",
            }}
          >
            KNOW YOUR
            <br />
            LIMITS. SKI
            <br />
            BEYOND THEM.
          </h1>

          <button
            style={{
              backgroundColor: "transparent",
              border: "2px solid #CDFF9A",
              color: "#CDFF9A",
              padding: "15px 30px",
              fontSize: "14px",
              letterSpacing: "1px",
              cursor: "pointer",
              transition: "all 0.3s ease",
              fontWeight: "500",
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = "#CDFF9A";
              e.target.style.color = "#1a3d2e";
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = "transparent";
              e.target.style.color = "#CDFF9A";
            }}
          >
            VIEW MORE
          </button>
        </div>
      </div>
      <div style={{ width: "50%", position: "relative" }}>
        <img src={img02} alt="Logo" style={{ width: "100%", height: "auto" }} />

        <div
          style={{
            position: "absolute",
            right: "-100px",
            bottom: "90px",
            transform: "translateX(-50%)",
            display: "flex",
            alignItems: "end",
            gap: "20px",

            
            
            backdropFilter: "blur(10px)",
          }}
        >
          <div
            style={{
              width: "100px",
              height: "2px",
              backgroundColor: "#ffffff",
              position: "relative",
            }}
          >
            <div
              style={{
                position: "absolute",
                left: "-5px",
                top: "-3px",
                width: "0",
                height: "0",
                borderRight: "8px solid #ffffff",
                borderTop: "4px solid transparent",
                borderBottom: "4px solid transparent",
              }}
            />
          </div>
          <div
            style={{
              width: "100px",
              height: "2px",
              backgroundColor: "#ffffff",
              position: "relative",
            }}
          >
            <div
              style={{
                position: "absolute",
                right: "-5px",
                top: "-3px",
                width: "0",
                height: "0",
                borderLeft: "8px solid #ffffff",
                borderTop: "4px solid transparent",
                borderBottom: "4px solid transparent",
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
